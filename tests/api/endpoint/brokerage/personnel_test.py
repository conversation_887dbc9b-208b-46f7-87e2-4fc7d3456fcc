from http import HTTPStatus

import pytest
from fastapi.testclient import Test<PERSON>lient


@pytest.fixture
def user_data():
    def _user_data(role: str):
        return {
            "name": "User",
            "email": "<EMAIL>",
            "password": "123456",
            "phone": "6041234567",
            "province": "BC",
            "city": "VA",
            "address": "123 Street",
            "postal_code": "V6B 1A1",
            "user_role": role,
        }

    return _user_data


def test_create_user_success(test_client: TestClient, personnel_api_prefix: str, auth_headers_generator, user_data):
    role = "SUPPORT"
    data = user_data(role)
    headers = auth_headers_generator("<EMAIL>")
    response = test_client.post(personnel_api_prefix, json=data, headers=headers)
    assert response.status_code == HTTPStatus.OK
    response_json = response.json()
    assert response_json["code"] == 1000
    created_user = response_json["data"]
    assert created_user["name"] == data["name"]
    assert created_user["email"] == data["email"]
    assert created_user["phone"] == data["phone"]
    assert created_user["user_role"] == role


def test_create_user_login_success(
    test_client: TestClient, personnel_api_prefix: str, auth_headers_generator, user_data
):
    role = "BROKER"
    data = user_data(role)
    headers = auth_headers_generator("<EMAIL>")

    # 创建用户
    create_response = test_client.post(personnel_api_prefix, json=data, headers=headers)
    assert create_response.status_code == HTTPStatus.OK
    create_response_json = create_response.json()
    assert create_response_json["code"] == 1000
    created_user = create_response_json["data"]
    assert created_user["name"] == data["name"]
    assert created_user["email"] == data["email"]
    assert created_user["phone"] == data["phone"]
    assert created_user["user_role"] == role
    login_data = {"username": data["email"], "password": data["password"]}
    login_response = test_client.post("/api/v1/insurance/auth/login", json=login_data)
    assert login_response.status_code == HTTPStatus.OK
    login_response_json = login_response.json()
    assert login_response_json["code"] == 1000
    token_data = login_response_json["data"]
    assert "access_token" in token_data
    assert "token_type" in token_data
    assert token_data["token_type"] == "Bearer"


@pytest.mark.parametrize(
    "missing_field",
    ["name", "email", "password", "user_role"],
    ids=["missing_name", "missing_email", "missing_password", "missing_role"],
)
def test_create_user_missing_required_fields(
    test_client: TestClient, personnel_api_prefix: str, auth_headers_generator, user_data, missing_field
):
    role = "SUPPORT"
    data = user_data(role)
    headers = auth_headers_generator("<EMAIL>")
    incomplete_data = data.copy()
    del incomplete_data[missing_field]
    response = test_client.post(personnel_api_prefix, json=incomplete_data, headers=headers)
    if missing_field == "user_role":
        assert response.json()["code"] == 1000
    else:
        assert response.json()["code"] == 4030


def test_create_user_duplicate_email(
    test_client: TestClient, personnel_api_prefix: str, auth_headers_generator, user_data
):
    data = user_data("SUPPORT")
    headers = auth_headers_generator("<EMAIL>")
    first_response = test_client.post(personnel_api_prefix, json=data, headers=headers)
    assert first_response.status_code == HTTPStatus.OK
    second_response = test_client.post(personnel_api_prefix, json=data, headers=headers)
    response_json = second_response.json()
    assert response_json["code"] == 4030
    assert "Email's user already exists" == response_json["message"]


def test_create_user_unauthorized(test_client: TestClient, personnel_api_prefix: str, user_data):
    data = user_data("SUPPORT")
    response = test_client.post(personnel_api_prefix, json=data)
    assert response.status_code in [HTTPStatus.UNAUTHORIZED, HTTPStatus.FORBIDDEN]


def test_get_brokerage_user_by_id_success(
    test_client: TestClient, personnel_api_prefix: str, auth_headers_generator, user_data
):
    role = "SUPPORT"
    data = user_data(role)
    headers = auth_headers_generator("<EMAIL>")
    create_response = test_client.post(personnel_api_prefix, json=data, headers=headers)
    assert create_response.status_code == HTTPStatus.OK
    created_user = create_response.json()["data"]
    user_id = created_user["id"]
    get_response = test_client.get(f"{personnel_api_prefix}/{user_id}", headers=headers)
    assert get_response.status_code == HTTPStatus.OK
    response_json = get_response.json()
    assert response_json["code"] == 1000
    user_info = response_json["data"]
    assert user_info["id"] == user_id
    assert user_info["name"] == data["name"]
    assert user_info["email"] == data["email"]
    assert user_info["phone"] == data["phone"]
    assert user_info["user_role"] == role


def test_get_brokerage_user_by_nonexistent_id(
    test_client: TestClient, personnel_api_prefix: str, auth_headers_generator
):
    headers = auth_headers_generator("<EMAIL>")
    nonexistent_id = 99999
    response = test_client.get(f"{personnel_api_prefix}/{nonexistent_id}", headers=headers)
    assert response.status_code == HTTPStatus.NOT_FOUND
    response_json = response.json()
    assert response_json["code"] == 4010
    assert "BrokerageUser not found" == response_json["message"]


def test_get_brokerage_user_unauthorized(test_client: TestClient, personnel_api_prefix: str):
    response = test_client.get(f"{personnel_api_prefix}/1")
    assert response.status_code in [HTTPStatus.UNAUTHORIZED, HTTPStatus.FORBIDDEN]
