from unittest.mock import AsyncMock
from unittest.mock import patch

import pytest

from bethune.model import Broker
from bethune.model import BrokerPaymentMethod
from bethune.model import BrokerProfile
from bethune.model import BrokerQualification
from bethune.model.base import GenderEnum
from bethune.model.payment import AccountTypeEnum
from bethune.repository.insurance.broker import BrokerRepository
from bethune.repository.insurance.broker_lead_config import BrokerLeadConfigRepository
from bethune.repository.insurance.broker_payment_method import BrokerPaymentMethodRepository
from bethune.repository.insurance.broker_profile import BrokerProfileRepository
from bethune.repository.insurance.broker_qualification import BrokerQualificationRepository
from bethune.repository.system.user import UserRepository
from bethune.service.insurance.broker import BrokerService


@pytest.fixture
def mock_repo(mocker):
    return mocker.Mock(spec=BrokerRepository)


@pytest.fixture
def mock_user_repo(mocker):
    return mocker.Mock(spec=UserRepository)


@pytest.fixture
def mock_broker_lead_config_repo(mocker):
    return mocker.Mock(spec=BrokerLeadConfigRepository)


@pytest.fixture
def mock_broker_payment_method_repo(mocker):
    return mocker.Mock(spec=BrokerPaymentMethodRepository)


@pytest.fixture
def mock_broker_profile_repo(mocker):
    return mocker.Mock(spec=BrokerProfileRepository)


@pytest.fixture
def mock_broker_qualification_repo(mocker):
    return mocker.Mock(spec=BrokerQualificationRepository)


@pytest.fixture
def broker_service_mock(
    mock_repo,
    mock_user_repo,
    mock_broker_lead_config_repo,
    mock_broker_payment_method_repo,
    mock_broker_profile_repo,
    mock_broker_qualification_repo,
):
    return BrokerService(
        mock_repo,
        mock_user_repo,
        mock_broker_lead_config_repo,
        mock_broker_payment_method_repo,
        mock_broker_profile_repo,
        mock_broker_qualification_repo,
    )


@pytest.fixture
def broker_service():
    return BrokerService(
        BrokerRepository(),
        UserRepository(),
        BrokerLeadConfigRepository(),
        BrokerPaymentMethodRepository(),
        BrokerProfileRepository(),
        BrokerQualificationRepository(),
    )


@pytest.fixture
def mock_broker():
    return Broker(
        id=1,
        user_id=1001,
        uid="BR-TEST001",
        name="John Doe",
        gender=GenderEnum.MALE,
        address="123 Main St",
        province="BC",
        city="Vancouver",
        postal_code="V6B 1A1",
        phone="555-1234",
        insurance_company="ABC Insurance",
        description="Test broker for unit testing",
    )


@pytest.fixture
def mock_broker_profile():
    return BrokerProfile(
        id=1,
        broker_id=1,
        public_fields="NAME|EMAIL|PHONE",
    )


@pytest.fixture
def mock_broker_qualification():
    return BrokerQualification(
        id=1,
        broker_profile_id=1,
        is_qualified=True,
    )


@pytest.fixture
def mock_broker_payment_method():
    return BrokerPaymentMethod(
        id=1,
        broker_id=1,
        account_type=AccountTypeEnum.E_TRANSFER,
        account_number="**********",
        is_default=True,
    )


class TestBrokerService:

    def test_get_by_user_id(self, broker_service_mock, mock_repo, mock_broker):
        mock_repo.get_by_user_id.return_value = mock_broker
        result = broker_service_mock.get_by_user_id(mock_broker.user_id)
        assert result == mock_broker
        mock_repo.get_by_user_id.assert_called_once_with(mock_broker.user_id, False)

    def test_get_by_user_id_not_found(self, broker_service_mock, mock_repo):
        mock_repo.get_by_user_id.return_value = None
        result = broker_service_mock.get_by_user_id(999)
        assert result is None
        mock_repo.get_by_user_id.assert_called_once_with(999, False)

    def test_update_broker_basic_info(self, broker_service):
        broker = broker_service.get_by_user_id(1000)
        assert broker
        broker.name = "Updated Test Broker Name"
        broker.phone = "555-9999"

        updated_broker = broker_service.update(broker)

        assert updated_broker.name == "Updated Test Broker Name"
        assert updated_broker.phone == "555-9999"
        assert updated_broker.id == broker.id

        db_broker = broker_service.get_by_user_id(broker.user_id)
        assert db_broker.name == "Updated Test Broker Name"
        assert db_broker.phone == "555-9999"

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "broker_id,tags,expected_result",
        [
            (100, set(), 0),
            (101, {"tag1"}, 1),
            (102, {"tag1", "tag2"}, 2),
            (103, {"tag1", "tag2", "tag3"}, 3),
        ],
    )
    async def test_add_tags_parametrized(self, broker_service, broker_id, tags, expected_result):
        if len(tags) == 0:
            result = await broker_service.add_tags(broker_id, tags)
            assert result == expected_result
        else:
            with patch("bethune.service.insurance.broker.get_redis") as mock_get_redis:
                mock_redis = AsyncMock()
                mock_redis.sadd.return_value = expected_result
                mock_get_redis.return_value = mock_redis

                result = await broker_service.add_tags(broker_id, tags)

                assert result == expected_result
                mock_redis.sadd.assert_called_once_with(f"bethune:broker:tags:{broker_id}", *tags)

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "broker_id,mock_return,expected_result",
        [
            (100, {"tag1", "tag2"}, {"tag1", "tag2"}),  # 有标签
            (101, {"python", "fastapi"}, {"python", "fastapi"}),  # 技术标签
            (102, None, set()),  # 无标签（返回None）
            (103, set(), set()),  # 无标签（返回空集合）
        ],
    )
    async def test_get_tags_parametrized(self, broker_service, broker_id, mock_return, expected_result):
        with patch("bethune.service.insurance.broker.get_redis") as mock_get_redis:
            mock_redis = AsyncMock()
            mock_redis.smembers.return_value = mock_return
            mock_get_redis.return_value = mock_redis

            result = await broker_service.get_tags(broker_id)

            assert result == expected_result
            mock_redis.smembers.assert_called_once_with(f"bethune:broker:tags:{broker_id}")

    def test_get_broker_cache_by_id(self, broker_service):
        result = broker_service.get_broker_cache_by_id(100)
        assert result is not None
        assert result.id == 100
        assert result.name == "John Doe Test Broker"
        assert result.uid == "BR-TEST100"

    def test_get_by_uid(self, broker_service):
        result = broker_service.get_by_uid("BR-TEST100")
        assert result is not None
        assert result.id == 100
        assert result.uid == "BR-TEST100"
        assert result.name == "John Doe Test Broker"

    @pytest.mark.parametrize(
        "broker_ids,expected_count,expected_ids",
        [
            ([100], 1, [100]),  # 单个存在的ID
            ([100, 101], 2, [100, 101]),  # 多个存在的ID
            ([100, 101, 102], 3, [100, 101, 102]),  # 更多存在的ID
            ([100, 999], 1, [100]),  # 部分存在的ID
            ([999, 998], 0, []),  # 全部不存在的ID
            ([], 0, []),  # 空列表
            ([100, 101, 102, 103, 104], 5, [100, 101, 102, 103, 104]),  # 所有测试数据
        ],
    )
    def test_get_by_ids_parametrized(self, broker_service, broker_ids, expected_count, expected_ids):
        result = broker_service.get_by_ids(broker_ids)
        assert len(result) == expected_count

        if expected_count > 0:
            result_ids = [b.id for b in result]
            for expected_id in expected_ids:
                assert expected_id in result_ids

    @pytest.mark.parametrize(
        "user_id,expected_total,expected_friend_ids",
        [
            (1000, 3, [101, 102, 103]),  # 主测试用户，推荐了3个人
            (1004, 0, []),  # 独立用户，没有推荐任何人
        ],
    )
    def test_my_friends_parametrized(self, broker_service, user_id, expected_total, expected_friend_ids):
        total, friends = broker_service.my_friends(user_id)
        assert total == expected_total
        assert len(friends) == expected_total

        if expected_total > 0:
            friend_ids = [f.id for f in friends]
            for expected_id in expected_friend_ids:
                assert expected_id in friend_ids

    @pytest.mark.parametrize(
        "user_id,offset,limit,expected_total,expected_page_size",
        [
            (1000, 0, 2, 3, 2),
            (1000, 2, 2, 3, 1),
            (1000, 0, 10, 3, 3),
            (1004, 0, 10, 0, 0),
        ],
    )
    def test_my_friends_pagination_parametrized(
        self, broker_service, user_id, offset, limit, expected_total, expected_page_size
    ):
        total, friends = broker_service.my_friends(user_id, offset, limit)
        assert total == expected_total
        assert len(friends) == expected_page_size

    def test_get_by_ids_empty_list(self, broker_service):
        result = broker_service.get_by_ids([])
        assert result == []

    def test_get_by_user_id_with_allow_none_true(self, broker_service):
        result = broker_service.get_by_user_id(999, allow_none=True)
        assert result is None

    def test_get_by_user_id_with_allow_none_false(self, broker_service):
        result = broker_service.get_by_user_id(1000, allow_none=False)
        assert result is not None
        assert result.id == 100
        assert result.user_id == 1000
        assert result.name == "John Doe Test Broker"

    def test_get_by_ids_mixed_scenarios(self, broker_service):
        result = broker_service.get_by_ids([100, 101, 102])
        assert len(result) >= 1

        result = broker_service.get_by_ids([100, 99999])
        assert len(result) == 1
        assert result[0].id == 100

        result = broker_service.get_by_ids([99998, 99999])
        assert result == []

    @pytest.mark.asyncio
    async def test_comprehensive_broker_operations(self, broker_service):
        broker = broker_service.get_by_user_id(1000)
        assert broker is not None
        assert broker.name == "John Doe Test Broker"

        broker.name = "Updated John Doe"
        broker.phone = "555-8888"
        updated_broker = broker_service.update(broker)
        assert updated_broker.name == "Updated John Doe"
        assert updated_broker.phone == "555-8888"

        by_uid = broker_service.get_by_uid("BR-TEST100")
        assert by_uid.name == "Updated John Doe"

        with patch("bethune.service.insurance.broker.get_redis") as mock_get_redis:
            mock_redis = AsyncMock()
            mock_redis.sadd.return_value = 2
            mock_redis.smembers.return_value = {"python", "insurance"}
            mock_get_redis.return_value = mock_redis

            tags = {"python", "insurance"}
            add_result = await broker_service.add_tags(100, tags)
            assert add_result == 2

            get_result = await broker_service.get_tags(100)
            assert get_result == {"python", "insurance"}

        total, friends = broker_service.my_friends(1000)
        assert total == 3
        assert len(friends) == 3

        all_brokers = broker_service.get_by_ids([100, 101, 102, 103, 104])
        assert len(all_brokers) == 5
