from bethune.model.broker import BrokerProfile
from bethune.repository.insurance import BrokerProfileRepository
from bethune.service.base import BaseService


class BrokerProfileService(BaseService[BrokerProfile, BrokerProfileRepository]):

    def __init__(self, repository: BrokerProfileRepository):
        super().__init__(repository)

    def get_by_broker_id(self, broker_id: int):
        return self.repository.get_by_broker_id(broker_id)
