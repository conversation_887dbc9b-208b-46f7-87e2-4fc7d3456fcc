from bethune.api.dto.base import BusinessType as DtoBusinessType
from bethune.api.dto.base import InsuranceType as InsuranceTypeDto
from bethune.api.dto.broker import BrokerCreateBase as BrokerCreateBaseDto
from bethune.api.dto.broker import BrokerLeadConfig as BrokerLeadConfigDto
from bethune.api.dto.broker import BrokerLeadFee as BrokerLeadFeeDto
from bethune.api.dto.broker import BrokerPaymentMethod as BrokerPaymentMethodDto
from bethune.api.dto.broker import BrokerProfile as BrokerProfileDto
from bethune.api.dto.broker import BrokerQualification as BrokerQualificationDto
from bethune.api.dto.reminder import ReminderConfigCreate as ReminderConfigCreateDto
from bethune.api.dto.reminder import ReminderType as DtoReminderType
from bethune.cache import cache
from bethune.db.redis import get_redis
from bethune.model import Broker
from bethune.model import User
from bethune.model.base import InsuranceType
from bethune.model.broker import BrokerRichInfoComposite
from bethune.model.reminder import NotifyMethod
from bethune.repository.base import DEFAULT_LIMIT
from bethune.repository.insurance import BrokerLeadConfigRepository
from bethune.repository.insurance import BrokerProfileRepository
from bethune.repository.insurance import BrokerQualificationRepository
from bethune.repository.insurance import BrokerRepository
from bethune.repository.insurance.broker_payment_method import BrokerPaymentMethodRepository
from bethune.repository.system import UserRepository
from bethune.service.base import BaseService
from bethune.service.core import ReferenceTypeEnum
from bethune.service.core.ref_code import ReferenceCodeService
from bethune.service.insurance.broker_lead_config import BrokerLeadConfigService
from bethune.service.insurance.broker_lead_fee import BrokerLeadFeeService
from bethune.service.insurance.broker_payment_method import BrokerPaymentMethodService
from bethune.service.insurance.broker_profile import BrokerProfileService
from bethune.service.insurance.broker_qualification import BrokerQualificationService
from bethune.service.reminder import ReminderConfigService

_REMINDER_FIRST_REMINDER_DAYS = 30
_BROKER_NOTIFY_METHODS = [NotifyMethod.EMAIL.value, NotifyMethod.INBOX.value]
_CUSTOMER_NOTIFY_METHODS = [NotifyMethod.EMAIL.value]


_BROKER_REMINDER_CONFIG_POLICY_RENEWAL = ReminderConfigCreateDto(
    business_type=DtoBusinessType.POLICY_RENEWAL,
    reminder_type=DtoReminderType.BROKER,
    first_reminder_days=_REMINDER_FIRST_REMINDER_DAYS,
    notify_methods=_BROKER_NOTIFY_METHODS,
)

_CUSTOMER_REMINDER_CONFIG_POLICY_RENEWAL = ReminderConfigCreateDto(
    business_type=DtoBusinessType.POLICY_RENEWAL,
    reminder_type=DtoReminderType.CUSTOMER,
    first_reminder_days=_REMINDER_FIRST_REMINDER_DAYS,
    notify_methods=_CUSTOMER_NOTIFY_METHODS,
)


class BrokerService(BaseService[Broker, BrokerRepository]):

    def __init__(
        self,
        repository: BrokerRepository,
        user_repository: UserRepository,
        broker_lead_config_repository: BrokerLeadConfigRepository,
        broker_payment_method_repository: BrokerPaymentMethodRepository,
        broker_profile_repository: BrokerProfileRepository,
        broker_qualification_repository: BrokerQualificationRepository,
    ):
        super().__init__(repository)
        self.user_repository = user_repository
        self.broker_lead_config_repository = broker_lead_config_repository
        self.broker_payment_method_repository = broker_payment_method_repository
        self.broker_profile_repository = broker_profile_repository
        self.broker_qualification_repository = broker_qualification_repository

    def __broker_tags_key(self, broker_id: int) -> str:
        return f"bethune:broker:tags:{broker_id}"

    def create_broker(
        self,
        broker_dto: BrokerCreateBaseDto,
        user: User,
        reminder_config_service: ReminderConfigService,
        broker_profile_service: BrokerProfileService,
        broker_qualification_service: BrokerQualificationService,
        broker_lead_config_service: BrokerLeadConfigService,
        broker_lead_fee_service: BrokerLeadFeeService,
        broker_payment_method_service: BrokerPaymentMethodService,
        reference_code_service: ReferenceCodeService,
    ) -> Broker:
        created_broker = self.create(broker_dto.to_model(user.id, reference_code_service.gen_code(ReferenceTypeEnum.BROKER)))  # type: ignore
        broker_id: int = created_broker.id  # type: ignore
        broker_reminder_configs = [
            _BROKER_REMINDER_CONFIG_POLICY_RENEWAL.to_model(broker_id),  # type: ignore
            _CUSTOMER_REMINDER_CONFIG_POLICY_RENEWAL.to_model(broker_id),  # type: ignore
        ]

        reminder_config_service.create_all(broker_reminder_configs)

        created_broker_profile = broker_profile_service.create(BrokerProfileDto(broker_id=broker_id).to_model())
        broker_qualification_service.create(
            BrokerQualificationDto(
                is_qualified=broker_dto.is_qualified, broker_profile_id=created_broker_profile.id
            ).to_model()
        )
        broker_lead_config_service.create(
            BrokerLeadConfigDto(
                broker_id=broker_id,
                insurance_type=[InsuranceTypeDto.HOUSE_INSURANCE.value, InsuranceTypeDto.RENTERS_INSURANCE.value],
            ).to_model(),
        )
        broker_lead_fee_service.create_all(
            [
                BrokerLeadFeeDto(insurance_type=InsuranceTypeDto.HOUSE_INSURANCE).to_model(broker_id=broker_id),
                BrokerLeadFeeDto(insurance_type=InsuranceTypeDto.RENTERS_INSURANCE).to_model(broker_id=broker_id),
            ]
        )
        broker_payment_method_service.create(BrokerPaymentMethodDto(broker_id=broker_id).to_model())
        return created_broker

    @cache.cache_on_arguments(namespace="broker-cache", expiration_time=60 * 60 * 12)
    def get_broker_cache_by_id(self, id: int) -> Broker:
        return self.repository.get_by_id(id)

    def get_by_id(self, id: int) -> Broker:
        return self.repository.get_by_id(id)

    def get_by_uid(self, uid: str) -> Broker:
        return self.repository.get_by_uid(uid)

    def get_by_ids(self, ids: list[int]) -> list[Broker]:
        return self.repository.get_by_ids(ids)

    def get_by_user_id(self, user_id: int, allow_none: bool = False) -> Broker | None:
        return self.repository.get_by_user_id(user_id, allow_none)

    async def add_tags(self, broker_id: int, tags: set[str]) -> int:
        if not tags:
            return 0
        return await get_redis().sadd(self.__broker_tags_key(broker_id), *tags)

    async def get_tags(self, broker_id: int) -> set[str]:
        tags = await get_redis().smembers(self.__broker_tags_key(broker_id))
        return set(tags) if tags else set()

    def my_friends(self, user_id: int, offset: int = 0, limit: int = DEFAULT_LIMIT):
        referer_ids = self.user_repository.get_referer_ids(user_id)
        if not referer_ids:
            return 0, []
        return self.repository.get_broker_friends(referer_ids, offset, limit)

    def find_candidate_brokers_for_creating_lead(
        self,
        current_broker: Broker,
        insurance_type: InsuranceType,
        customer_province: str,
        customer_city: str,
        keyword: str | None = None,
        page_no: int = 1,
        page_size: int = 20,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[BrokerRichInfoComposite], BrokerRichInfoComposite | None]:
        return self.repository.get_lead_candidate_brokers(
            current_broker, insurance_type, customer_province, customer_city, keyword, page_no, page_size, offset, limit
        )
