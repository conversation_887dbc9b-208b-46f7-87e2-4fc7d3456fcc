from bethune.model.broker import Broker<PERSON>aymentMethod
from bethune.repository.insurance.broker_payment_method import BrokerPaymentMethodRepository
from bethune.service.base import BaseService


class BrokerPaymentMethodService(BaseService[BrokerPaymentMethod, BrokerPaymentMethodRepository]):

    def __init__(self, repository: BrokerPaymentMethodRepository):
        super().__init__(repository)

    def get_by_broker_id(self, broker_id: int):
        return self.repository.get_by_broker_id(broker_id)
