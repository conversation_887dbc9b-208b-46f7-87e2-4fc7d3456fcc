from bethune.model.base import InsuranceType
from bethune.model.broker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from bethune.repository.insurance import Broker<PERSON><PERSON><PERSON>eeRepository
from bethune.service.base import BaseService


class BrokerLeadFeeService(BaseService[BrokerLeadFee, BrokerLeadFeeRepository]):

    def __init__(self, repository: BrokerLeadFeeRepository):
        super().__init__(repository)

    def get_by_broker_id(self, broker_id: int) -> list[BrokerLeadFee]:
        return self.repository.get_by_broker_id(broker_id)

    def get_by_broker_id_and_insurance_type(
        self, broker_id: int, insurance_type: InsuranceType
    ) -> BrokerLeadFee | None:
        return self.repository.get_by_broker_id_and_insurance_type(broker_id, insurance_type)

    def delete_by_broker_id(self, broker_id: int):
        self.repository.delete_by_broker_id(broker_id)
