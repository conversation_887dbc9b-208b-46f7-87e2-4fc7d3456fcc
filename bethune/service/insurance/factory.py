from .broker import BrokerService
from .broker_lead_config import BrokerLeadConfigService
from .broker_lead_fee import BrokerLeadFeeService
from .broker_payment_method import BrokerPaymentMethodService
from .broker_profile import BrokerProfileService
from .broker_qualification import BrokerQualificationService
from .brokerage import BrokerageService
from .brokerage_trial_application import BrokerageTrialApplicationService
from .brokerage_user import BrokerageUserService
from .customer import CustomerService
from .insurance_application import InsuranceApplicationService
from .insurance_company import InsuranceCompanyService
from .insurance_consultation import InsuranceConsultationService
from .insurance_policy import InsurancePolicyService
from .lead import LeadApplicationService
from .lead import LeadReferralFeePaymentService
from .lead import LeadService
from .promotion_material import PromotionMaterialService
from bethune.repository.insurance.factory import InsuranceRepositoryFactory
from bethune.repository.system.factory import SystemRepositoryFactory
from bethune.service.insurance.insurance_consultation_application import InsuranceConsultationApplicationService
from bethune.service.insurance.insurance_consultation_customer import InsuranceConsultationCustomerService
from bethune.service.insurance.user_feedback import UserFeedbackService


class ServiceFactory:

    @staticmethod
    def create_insurance_company_service() -> InsuranceCompanyService:
        return InsuranceCompanyService(InsuranceRepositoryFactory.create_insurance_company_repository())

    @staticmethod
    def create_customer_service() -> CustomerService:
        return CustomerService(
            InsuranceRepositoryFactory.create_customer_repository(),
            InsuranceRepositoryFactory.create_insurance_application_repository(),
        )

    @staticmethod
    def create_insurance_application_service() -> InsuranceApplicationService:
        return InsuranceApplicationService(
            InsuranceRepositoryFactory.create_insurance_application_repository(),
            InsuranceRepositoryFactory.create_lead_application_repository(),
        )

    @staticmethod
    def create_broker_service() -> BrokerService:
        return BrokerService(
            InsuranceRepositoryFactory.create_broker_repository(),
            SystemRepositoryFactory.create_user_repository(),
            InsuranceRepositoryFactory.create_broker_lead_config_repository(),
            InsuranceRepositoryFactory.create_broker_payment_method_repository(),
            InsuranceRepositoryFactory.create_broker_profile_repository(),
            InsuranceRepositoryFactory.create_broker_qualification_repository(),
        )

    @staticmethod
    def create_lead_service() -> LeadService:
        return LeadService(InsuranceRepositoryFactory.create_lead_repository())

    @staticmethod
    def create_lead_referral_fee_payment_service() -> LeadReferralFeePaymentService:
        return LeadReferralFeePaymentService(
            InsuranceRepositoryFactory.create_lead_referral_fee_payment_repository(),
            InsuranceRepositoryFactory.create_lead_repository(),
        )

    @staticmethod
    def create_lead_application_service() -> LeadApplicationService:
        return LeadApplicationService(InsuranceRepositoryFactory.create_lead_application_repository())

    @staticmethod
    def create_insurance_policy_service() -> InsurancePolicyService:
        return InsurancePolicyService(InsuranceRepositoryFactory.create_insurance_policy_repository())

    @staticmethod
    def create_broker_profile_service() -> BrokerProfileService:
        return BrokerProfileService(InsuranceRepositoryFactory.create_broker_profile_repository())

    @staticmethod
    def create_broker_qualification_service() -> BrokerQualificationService:
        return BrokerQualificationService(InsuranceRepositoryFactory.create_broker_qualification_repository())

    @staticmethod
    def create_broker_lead_config_service() -> BrokerLeadConfigService:
        return BrokerLeadConfigService(InsuranceRepositoryFactory.create_broker_lead_config_repository())

    @staticmethod
    def create_broker_lead_fee_service() -> BrokerLeadFeeService:
        return BrokerLeadFeeService(InsuranceRepositoryFactory.create_broker_lead_fee_repository())

    @staticmethod
    def create_broker_payment_method_service() -> BrokerPaymentMethodService:
        return BrokerPaymentMethodService(InsuranceRepositoryFactory.create_broker_payment_method_repository())

    @staticmethod
    def create_user_feedback_service() -> UserFeedbackService:
        return UserFeedbackService(InsuranceRepositoryFactory.create_user_feedback_repository())

    @staticmethod
    def create_brokerage_service() -> BrokerageService:
        return BrokerageService(InsuranceRepositoryFactory.create_brokerage_repository())

    @staticmethod
    def create_brokerage_user_service() -> BrokerageUserService:
        return BrokerageUserService(InsuranceRepositoryFactory.create_brokerage_user_repository())

    @staticmethod
    def create_brokerage_trial_application_service() -> BrokerageTrialApplicationService:
        return BrokerageTrialApplicationService(
            InsuranceRepositoryFactory.create_brokerage_trial_application_repository()
        )

    @staticmethod
    def create_insurance_consultation_service() -> InsuranceConsultationService:
        return InsuranceConsultationService(InsuranceRepositoryFactory.create_insurance_consultation_repository())

    @staticmethod
    def create_promotion_service() -> PromotionMaterialService:
        return PromotionMaterialService(InsuranceRepositoryFactory.create_promotion_material_repository())

    @staticmethod
    def create_insurance_consultation_application_service() -> InsuranceConsultationApplicationService:
        return InsuranceConsultationApplicationService(
            InsuranceRepositoryFactory.create_insurance_consultation_application_repository()
        )

    @staticmethod
    def create_insurance_consultation_customer_service() -> InsuranceConsultationCustomerService:
        return InsuranceConsultationCustomerService(
            InsuranceRepositoryFactory.create_insurance_consultation_customer_repository()
        )
