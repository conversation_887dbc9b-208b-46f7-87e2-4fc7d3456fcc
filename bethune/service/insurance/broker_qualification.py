from bethune.model.broker import BrokerQualification
from bethune.repository.insurance import BrokerQualificationRepository
from bethune.service.base import BaseService


class BrokerQualificationService(BaseService[BrokerQualification, BrokerQualificationRepository]):

    def __init__(self, repository: BrokerQualificationRepository):
        super().__init__(repository)

    def get_by_profile_id(self, profile_id: int):
        return self.repository.get_by_profile_id(profile_id)
