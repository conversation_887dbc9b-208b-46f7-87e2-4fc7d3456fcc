from bethune.model.broker import BrokerLeadConfig
from bethune.repository.insurance.broker_lead_config import BrokerLeadConfigRepository
from bethune.service.base import BaseService


class BrokerLeadConfigService(BaseService[BrokerLeadConfig, BrokerLeadConfigRepository]):

    def __init__(self, repository: BrokerLeadConfigRepository):
        super().__init__(repository)

    def get_by_broker_id(self, broker_id: int):
        return self.repository.get_by_broker_id(broker_id)
