from bethune.model import BaseModel
from bethune.repository import BaseRepository


class BaseService[T: BaseModel, R: BaseRepository[T]]:  # type: ignore

    def __init__(self, repository: R):
        self.repository = repository

    def create(self, obj: T) -> T:
        return self.repository.create(obj)

    def create_all(self, objs: list[T]) -> list[T]:
        return self.repository.create_all(objs)

    def update(self, obj: T) -> T:
        return self.repository.update(obj)

    def get_by_id(self, id: int) -> T:
        return self.repository.get_by_id(id)

    def delete(self, obj: T) -> None:
        return self.repository.delete(obj)

    def delete_by_id(self, id: int) -> None:
        return self.repository.delete_by_id(id)

    def get_by_example(self, example: T | None = None, offset: int = 0, limit: int = 20) -> list[T]:
        return self.repository.get_by_example(example, offset, limit)

    def get_by_example_with_total(
        self, example: T | None = None, offset: int = 0, limit: int = 20
    ) -> tuple[int, list[T]]:
        total = self.repository.count_by_example(example)
        if total == 0:
            return total, []
        return total, self.repository.get_by_example(example, offset, limit)
