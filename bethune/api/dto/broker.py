from enum import StrEnum

from pydantic import BaseModel
from pydantic import model_validator
from pydantic.fields import Field

from bethune.api.dto.base import AuditMixin
from bethune.api.dto.base import ConsultationStatus
from bethune.api.dto.base import GenderEnum
from bethune.api.dto.base import InsuranceType
from bethune.api.dto.brokerage import BrokerageResponse
from bethune.model import Broker as BrokerModel
from bethune.model import BrokerLeadConfig as BrokerLeadConfigModel
from bethune.model import BrokerLeadFee as BrokerLeadFeeModel
from bethune.model import BrokerPaymentMethod as BrokerPaymentMethodModel
from bethune.model import BrokerProfile as BrokerProfileModel
from bethune.model import BrokerQualification as BrokerQualificationModel
from bethune.model.broker import BrokerRichInfoComposite
from bethune.model.broker import InsuranceConsultation as InsuranceConsultationModel
from bethune.model.broker import InsuranceConsultationApplication as InsuranceConsultationApplicationModel
from bethune.model.broker import InsuranceConsultationCustomer as InsuranceConsultationCustomerModel
from bethune.model.broker import PromotionMaterial as PromotionMaterialModel
from bethune.settings import settings
from bethune.util.captcha import hide_broker_non_public_fields
from bethune.util.password import is_empty_password


class ReferralFeeTypeEnum(StrEnum):
    FIXED = "FIXED"
    PREMIUM_PERCENTAGE = "PREMIUM_PERCENTAGE"


class BrokerQualification(BaseModel):
    broker_profile_id: int
    is_qualified: bool

    def to_model(self) -> BrokerQualificationModel:
        return BrokerQualificationModel(**self.model_dump(exclude_unset=True))


class BrokerQualificationUpdate(BaseModel):
    is_qualified: bool

    def to_model(self, id: int, broker_profile_id: int) -> BrokerQualificationModel:
        model = BrokerQualificationModel(**self.model_dump(exclude_unset=True))
        model.id = id
        model.broker_profile_id = broker_profile_id
        return model


class BrokerProfile(BaseModel):
    broker_id: int
    public_fields: list[str] = []

    def to_model(self) -> BrokerProfileModel:
        model = BrokerProfileModel(**self.model_dump(exclude_unset=True, exclude={"public_fields"}))
        if self.public_fields:
            model.public_fields = "|".join(self.public_fields)
        else:
            model.public_fields = ""
        return model

    @classmethod
    def from_model(cls, model: BrokerProfileModel) -> "BrokerProfile":
        dto = cls(
            **model.model_dump(exclude={"public_fields"}),
        )
        if model.public_fields:
            dto.public_fields = model.public_fields.split("|")
        return dto


class BrokerProfileUpdate(BaseModel):
    public_fields: list[str] = []

    def to_model(self, id: int, broker_id: int) -> BrokerProfileModel:
        model = BrokerProfileModel(**self.model_dump(exclude_unset=True, exclude={"public_fields"}))
        model.id = id
        model.broker_id = broker_id
        if self.public_fields:
            model.public_fields = "|".join(self.public_fields)
        else:
            model.public_fields = ""
        return model


class BrokerPaymentMethod(BaseModel):
    broker_id: int
    account_number: str = ""

    def to_model(self) -> BrokerPaymentMethodModel:
        return BrokerPaymentMethodModel(**self.model_dump(exclude_unset=True))

    @classmethod
    def from_model(cls, model: BrokerPaymentMethodModel) -> "BrokerPaymentMethod":
        return cls(**model.model_dump())


class BrokerPaymentMethodUpdate(BaseModel):
    account_number: str | None = None

    def to_model(self, id: int, broker_id: int) -> BrokerPaymentMethodModel:
        model = BrokerPaymentMethodModel(**self.model_dump(exclude_unset=True))
        model.id = id
        model.broker_id = broker_id
        return model


class BrokerLeadFee(BaseModel):
    broker_id: int | None = None
    insurance_type: InsuranceType
    referral_fee_type: ReferralFeeTypeEnum | None = None
    referral_fee_value: float | None = None

    def to_model(self, broker_id: int) -> BrokerLeadFeeModel:
        model = BrokerLeadFeeModel(**self.model_dump(exclude_unset=True))
        model.broker_id = broker_id
        return model

    @classmethod
    def from_model(cls, model: BrokerLeadFeeModel):
        return cls(**model.model_dump())

    @classmethod
    def from_models(cls, models: list[BrokerLeadFeeModel]):
        return [cls.from_model(model) for model in models]


class BrokerLeadConfig(BaseModel):
    broker_id: int
    insurance_type: list[str]  # type: ignore
    allow_leads: bool | None = None
    lead_fees: list[BrokerLeadFee] | None = None
    broker_profile: BrokerProfile | None = None
    willing_pay_for_leads: bool | None = None

    def to_model(self) -> BrokerLeadConfigModel:
        model = BrokerLeadConfigModel(**self.model_dump(exclude_unset=True))
        model.insurance_type_list = self.insurance_type  # type: ignore
        return model

    @classmethod
    def from_model(
        cls,
        model: BrokerLeadConfigModel,
        lead_fees: list[BrokerLeadFee],
        broker_profile: BrokerProfileModel | None = None,
    ):
        model.insurance_type = model.insurance_type_list  # type: ignore
        new_instance = cls(**model.__dict__)

        if lead_fees:
            new_instance.lead_fees = BrokerLeadFee.from_models(lead_fees)

        if broker_profile is not None:
            new_instance.broker_profile = BrokerProfile.from_model(broker_profile)
        return new_instance


class BrokerLeadConfigUpdate(BaseModel):
    allow_leads: bool | None = None
    insurance_type: list[InsuranceType] | None = None
    referral_fee_type: ReferralFeeTypeEnum | None = None
    referral_fee_value: float | None = None
    broker_profile: BrokerProfileUpdate | None = None
    lead_fees: list[BrokerLeadFee] | None = None
    willing_pay_for_leads: bool | None = None

    def to_model(self, id, broker_id: int) -> BrokerLeadConfigModel:
        model = BrokerLeadConfigModel(
            **self.model_dump(
                exclude_unset=True,
                exclude_none=True,
                exclude={"broker_id", "broker_profile", "lead_fees"},
            )
        )
        model.id = id
        model.broker_id = broker_id

        if self.insurance_type is not None:
            model.insurance_type_list = self.insurance_type  # type: ignore

        return model


class BrokerBase(BaseModel):
    uid: str | None = None
    user_id: int | None = None
    brokerage_id: int | None = None
    name: str
    gender: GenderEnum | None = None
    address: str
    province: str = ""
    city: str = ""
    postal_code: str = ""
    phone: str
    description: str | None = None


class BrokerCreateBase(BrokerBase):
    email: str
    password: str
    is_qualified: bool = False
    avatar: str | None = None
    referer_id: int | None = None

    def to_model(self, user_id: int, uid: str) -> BrokerModel:
        return BrokerModel(
            **self.model_dump(exclude_unset=True)
            | {
                "user_id": user_id,
                "uid": uid,
            }
        )


class BrokerCreate(BrokerCreateBase):
    password: str | None = None  # type: ignore
    verification_code: str | None = None
    oauth_id: str | None = None

    # add model before validator to check either [password, verification_code] or [oauth_id] is provided
    @model_validator(mode="before")
    @classmethod
    def check_password_verification_code_or_oauth_id(cls, values: dict) -> dict:
        if values.get("oauth_id") is None and (
            values.get("password") is None or values.get("verification_code") is None
        ):
            raise ValueError("Either [password, verifcation_code] or [oauth_id] must be provided")
        return values


class BrokerCreateForBrokerage(BrokerCreateBase):
    pass


class BrokerUpdate(BaseModel):
    name: str | None = None
    gender: GenderEnum | None = None
    province: str | None = None
    address: str | None = None
    city: str | None = None
    phone: str | None = None
    postal_code: str | None = None
    payment_method: BrokerPaymentMethodUpdate | None = None
    is_qualified: bool | None = None
    avatar: str | None = None
    description: str | None = None

    def to_model(self, id: int) -> BrokerModel:
        model = BrokerModel(
            **self.model_dump(
                exclude_unset=True,
                exclude={"user_id", "payment_method", "is_qualified", "avatar"},
            )
        )
        model.id = id
        return model


class Broker(BrokerBase):
    id: int

    permissions: set[str] = Field(default_factory=set)
    is_qualified: bool | None = None
    avatar: str | None = None
    email: str | None = None
    payment_method: BrokerPaymentMethod | None = None
    brokerage: BrokerageResponse | None = None
    is_empty_password: bool = False  # Indicates if the broker should set a password

    @classmethod
    def from_model(
        cls,
        model: BrokerModel,
        permissions: set[str] | None = None,
        is_qualified: bool | None = None,
        avatar: str | None = None,
        email: str | None = None,
        payment_method: BrokerPaymentMethod | None = None,
        brokerage: BrokerageResponse | None = None,
    ):
        return cls(
            **model.model_dump(),
            permissions=permissions or set(),
            is_qualified=is_qualified,
            avatar=avatar,
            email=email,
            payment_method=payment_method,
            brokerage=brokerage,
            is_empty_password=is_empty_password(model.user.password),  # type: ignore
        )

    @classmethod
    def from_models(cls, models: list[BrokerModel]):
        return [cls.from_model(model, None, model.profile.qualification.is_qualified) for model in models]


class BrokerList(BrokerBase, AuditMixin):

    @classmethod
    def from_model(cls, model: BrokerModel):
        return cls(**model.model_dump())

    @classmethod
    def from_models(cls, models: list[BrokerModel]):
        return [cls.from_model(broker) for broker in models]


class CandidateBrokerListForCreatingLead(BrokerBase, AuditMixin):
    id: int
    gender: GenderEnum | None = None
    email: str | None = None
    is_referer: bool | None = False  # 是否是当前代理人的引荐者
    willing_pay_for_leads: bool | None = False
    referral_fee_value: float | None = None
    referral_fee_type: ReferralFeeTypeEnum | None = None
    avatar: str | None = None
    brokerage_name: str | None = None

    @classmethod
    def assemble_avatar_url(cls, dumped_model: dict) -> dict:
        avatar = dumped_model.get("avatar")
        if avatar is not None:
            dumped_model["avatar"] = (
                f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{settings.AVATARS_FOLDER}/{avatar}"
            )
        return dumped_model

    @classmethod
    def from_model(cls, model: BrokerRichInfoComposite) -> "CandidateBrokerListForCreatingLead":
        return cls(
            **cls.assemble_avatar_url(
                hide_broker_non_public_fields(
                    model.model_dump(),
                    set(model.profile_public_fields.split("|") if model.profile_public_fields else ""),
                )
            ),
            referral_fee_type=model.lead_config_referral_fee_type,
            referral_fee_value=model.lead_config_referral_fee_value,
            willing_pay_for_leads=model.lead_config_willing_pay_for_leads,
        )

    @classmethod
    def from_models(
        cls,
        models: list[BrokerRichInfoComposite],
        first_element_is_referer: bool = False,
    ) -> list["CandidateBrokerListForCreatingLead"]:
        brokers = [cls.from_model(broker) for broker in models]
        if brokers:
            brokers[0].is_referer = first_element_is_referer
        return brokers


class BrokerChangePassword(BaseModel):
    origin_password: str
    new_password: str


class BrokerResetPassword(BaseModel):
    email: str
    new_password: str
    verification_code: str


class BrokerLeadSummary(BaseModel):
    total_lead_count: int
    total_lead_referral_fee: float


class BrokerBusinessCard(BaseModel):
    uid: str
    name: str
    phone: str
    email: str
    gender: GenderEnum | None = None
    description: str | None = None
    avatar: str | None = None
    insurance_products: list[InsuranceType]
    brokerage: BrokerageResponse | None = None

    @classmethod
    def from_model(cls, model: BrokerModel) -> "BrokerBusinessCard":
        return cls(
            **model.model_dump(exclude={"brokerage"}),
            avatar=(
                f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{settings.AVATARS_FOLDER}/{model.user.avatar}"
                if model.user.avatar
                else None
            ),
            insurance_products=model.lead_config.insurance_type_list,  # type: ignore
            email=model.user.email,
            brokerage=None if model.brokerage is None else BrokerageResponse(**model.brokerage.model_dump()),
        )


class PromotionMaterial(BaseModel):
    broker_id: int
    image: str | None = None
    image_url: str | None = None

    @classmethod
    def from_model(cls, model: PromotionMaterialModel):
        new_insurance = cls(**model.model_dump())
        new_insurance.image_url = (
            f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/promotion_material/{model.image}"
        )
        return new_insurance

    def to_model(self) -> PromotionMaterialModel:
        return PromotionMaterialModel(**self.model_dump(exclude_unset=True))


class PromotionMaterialList(PromotionMaterial, AuditMixin):
    id: int
    image_url: str | None = None

    @classmethod
    def from_model(cls, model: PromotionMaterialModel):
        new_insurance = cls(**model.model_dump())
        new_insurance.image_url = (
            f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/promotion_material/{model.image}"
        )
        return new_insurance

    @classmethod
    def from_models(cls, models: list[PromotionMaterialModel]):
        return [cls.from_model(broker) for broker in models]


class PromotionMaterialQueryFilter(BaseModel):
    broker_id: int

    def to_model(self) -> PromotionMaterialModel:
        return PromotionMaterialModel(**self.model_dump(exclude_unset=True, exclude_none=True))


class InsuranceConsultation(BaseModel):
    broker_id: int | None = None
    status: ConsultationStatus | None = None
    source: str = ""
    province: str = ""
    city: str = ""
    postal_code: str = ""
    phone: str = ""
    name: str = ""
    address: str = ""
    email: str | None = None
    remark: str | None = None

    @classmethod
    def from_model(cls, model: InsuranceConsultationModel):
        return cls(**model.model_dump())

    def to_model(self, broker_id: int) -> InsuranceConsultationModel:
        model = InsuranceConsultationModel(**self.model_dump(exclude_unset=True))
        model.broker_id = broker_id
        return model


class InsuranceConsultationList(InsuranceConsultation, AuditMixin):
    id: int
    application_id: int | None = None

    @classmethod
    def from_model(cls, model: InsuranceConsultationModel):
        new_insurance = cls(**model.model_dump())
        new_insurance.application_id = model.insurance_application.id if model.insurance_application else None
        return new_insurance

    @classmethod
    def from_models(cls, models: list[InsuranceConsultationModel]):
        return [cls.from_model(broker) for broker in models]


class InsuranceConsultationQueryFilter(BaseModel):
    broker_id: int

    def to_model(self) -> InsuranceConsultationModel:
        return InsuranceConsultationModel(**self.model_dump(exclude_unset=True, exclude_none=True))


class InsuranceConsultationApplication(BaseModel):
    insurance_consultation: int
    application_id: int

    def to_model(self) -> InsuranceConsultationApplicationModel:
        return InsuranceConsultationApplicationModel(**self.model_dump(exclude_unset=True))


class InsuranceConsultationCustomer(BaseModel):

    insurance_consultation: int
    customer_id: int

    def to_model(self) -> InsuranceConsultationCustomerModel:
        return InsuranceConsultationCustomerModel(**self.model_dump(exclude_unset=True))
