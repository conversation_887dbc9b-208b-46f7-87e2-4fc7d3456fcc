from datetime import date
from datetime import datetime
from datetime import timezone
from enum import Enum
from io import BytesIO
from typing import Any

from fastapi import APIRouter
from fastapi import Security
from fastapi.responses import StreamingResponse
from openpyxl import Workbook
from openpyxl.styles import Font

from bethune.api.endpoint.insurance.service_context import ServiceContext
from bethune.model import Broker

api_router = APIRouter(prefix="/report", tags=["report"])

_FIELD_MAPPINGS = {
    # 通用字段
    "ref_code": "参考编号",
    "status": "状态",
    "created_at": "创建时间",
    "updated_at": "更新时间",
    # 代理人相关字段
    "broker_id": "代理人ID",
    "broker_name": "代理人姓名",
    "email": "邮箱地址",
    # 客户相关字段
    "customer_id": "客户ID",
    "customer_name": "客户姓名",
    "customer_email": "客户邮箱",
    "customer_phone": "客户电话",
    "customer_address": "客户地址",
    "customer_birthday": "客户生日",
    "customer_city": "客户城市",
    "customer_gender": "客户性别",
    "customer_postal_code": "客户邮编",
    "customer_province": "客户省份",
    # 保险相关字段
    "insurance_type": "保险类型",
    "application_id": "订单ID",
    "premium": "保费",
    # 线索相关字段
    "created_by": "创建人ID",
    "created_by_name": "创建人姓名",
    "created_by_phone": "创建人电话",
    "assign_to": "分配人ID",
    "assign_to_name": "分配人姓名",
    "assign_to_broker_name": "分配代理人姓名",
    "assign_to_broker_address": "分配代理人地址",
    "assign_to_broker_city": "分配代理人城市",
    # 推荐费相关字段
    "lead_referral_fee_in_cents": "线索推荐费(分)",
    "payment_amount_in_cents": "支付金额(分)",
    # 经纪公司相关字段
    "brokerage_id": "经纪公司ID",
    "brokerage_name": "经纪公司名称",
    # 其他字段
    "name": "姓名",
    "phone": "电话",
    "address": "地址",
    "city": "城市",
    "province": "省份",
    "postal_code": "邮编",
}

# 定义各数据集要导出的字段
_BROKER_FIELDS = [
    "broker_name",
    "email",
    "brokerage_name",
    "broker_id",
]
_PAN_AGENT_FIELDS = ["broker_name", "email", "broker_id"]
_LEAD_FIELDS = [
    "insurance_type",
    "created_by_name",
    "customer_name",
    "customer_phone",
    "customer_gender",
    "customer_email",
    "customer_address",
    "customer_birthday",
    "customer_province",
    "customer_city",
    "customer_postal_code",
    "customer_id",
    "assign_to_broker_name",
    "assign_to_broker_address",
    "assign_to_broker_city",
    "status",
    "premium",
    "lead_referral_fee_in_cents",
    "brokerage_name",
    "application_id",
    "ref_code",
    "created_at",
]
_APPLICATION_FIELDS = [
    "brokerage_name",
    "customer_name",
    "customer_email",
    "customer_phone",
    "broker_name",
    "status",
    "insurance_type",
    "premium",
    "created_at",
    "ref_code",
]
_BROKERAGE_APPLICATION_FIELDS = [
    "status",
    "insurance_type",
    "customer_name",
    "customer_email",
    "customer_phone",
    "broker_name",
    "brokerage_name",
    "premium",
    "created_at",
    "ref_code",
]

# 定义非经纪行订单字段 - 与普通订单相似但不包含经纪公司相关字段
_NON_BROKERAGE_APPLICATION_FIELDS = [
    "status",
    "insurance_type",
    "customer_name",
    "customer_email",
    "customer_phone",
    "broker_name",
    "premium",
    "created_at",
    "ref_code",
]
_COMMON_DATE_COLUMNS = ["created_at", "updated_at"]

_BROKERAGE_GROUPING_APPLICATION_FIELDS = ["brokerage_name"] + [
    field for field in _BROKERAGE_APPLICATION_FIELDS if field != "brokerage_name"
]


def _adjust_column_width(worksheet):
    """设置工作表的列宽以确保列名完全显示"""
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter

        for cell in column:
            if cell.value:
                cell_length = len(str(cell.value))
                if cell_length > max_length:
                    max_length = cell_length

        adjusted_width = max_length + 4
        worksheet.column_dimensions[column_letter].width = adjusted_width


def _format_date(date_value: Any) -> str:
    if isinstance(date_value, datetime):
        return date_value.date().isoformat()
    if isinstance(date_value, date):
        return date_value.isoformat()
    return str(date_value) if date_value else ""


def _process_dict_object(obj: dict, prefix: str, result: dict) -> dict:
    for key, value in obj.items():
        new_key = f"{prefix}{key}" if prefix else key
        _flatten_object(value, f"{new_key}_", result)
    return result


def _process_class_object(obj: Any, prefix: str, result: dict) -> dict:
    for key, value in obj.__dict__.items():
        if not key.startswith("_") and not callable(value):
            new_key = f"{prefix}{key}" if prefix else key
            _flatten_object(value, f"{new_key}_", result)
    return result


def sort_data(data: list[dict[str, Any]], first_sort: str) -> list[dict[str, Any]]:
    if not data:
        return []

    sorted_items = sorted(
        data,
        key=lambda item: (item.get(first_sort) is None or item.get(first_sort) == "", item.get(first_sort) or ""),
    )
    groups = {
        key: [item for item in sorted_items if item.get(first_sort, "") == key]
        for key in {item.get(first_sort, "") for item in sorted_items}
    }

    return [
        item
        for group_key in [k for k, _ in sorted(groups.items(), key=lambda pair: (pair[0] == "", pair[0]))]
        for item in sorted(groups[group_key], key=lambda item: item.get("created_at", ""), reverse=True)
    ]


def _process_value_object(obj: Any, prefix: str, result: dict) -> dict:
    if isinstance(obj, datetime):
        result[prefix[:-1]] = obj.date()
    elif isinstance(obj, date):
        result[prefix[:-1]] = obj
    else:
        result[prefix[:-1]] = obj
    return result


def _flatten_object(obj: Any, prefix: str = "", result: dict | None = None) -> dict[str, Any]:
    """将嵌套对象扁平化为单层字典，特殊处理枚举类型和日期类型"""
    if result is None:
        result = {}
    if obj is None:
        return result

    if isinstance(obj, Enum):
        result[prefix[:-1] if prefix else "value"] = str(obj.value)
        return result

    # 处理不同类型的对象
    if isinstance(obj, dict):
        return _process_dict_object(obj, prefix, result)
    if hasattr(obj, "__dict__"):
        return _process_class_object(obj, prefix, result)
    return _process_value_object(obj, prefix, result)


def _create_broker_info(broker):
    return {
        "broker_id": broker.id,
        "broker_name": broker.name,
        "email": broker.user.email if broker.user else "",
        "brokerage_name": broker.brokerage.name if broker.brokerage else "",
    }


def _is_valid_broker(broker: Broker):
    return broker.profile and broker.profile.qualification and broker.profile.qualification.is_qualified


def _write_sheet_data(
    worksheet,
    data: list[dict[str, Any]],
    fields: list[str],
    field_mappings: dict[str, str],
    date_columns: list[str] | None = None,
):
    # 写入表头
    for col_idx, field in enumerate(fields, 1):
        cell = worksheet.cell(row=1, column=col_idx)
        cell.value = field_mappings.get(field, field)
        cell.font = Font(bold=True)

    # 写入数据
    for row_idx, item in enumerate(data, 2):
        for col_idx, field in enumerate(fields, 1):
            value = item.get(field, "")

            if date_columns and field in date_columns:
                value = _format_date(value)

            worksheet.cell(row=row_idx, column=col_idx, value=value)


@api_router.get(
    "/export",
    summary="导出经纪公司报表数据",
    description="查询经纪公司相关报表数据并导出",
    response_class=StreamingResponse,
)
async def export_brokerage_report(
    sc: ServiceContext = Security(ServiceContext.create, scopes=["brokerage:company:create"]),
) -> StreamingResponse:
    # 获取所有经纪人数据
    brokers = sc.broker_service.get_by_example(limit=1000)
    # 先按经纪公司名称排序（空值排在后面），再按代理人姓名排序
    qualified_brokers = sorted(
        [_create_broker_info(broker) for broker in brokers if _is_valid_broker(broker)],
        key=lambda x: (x["brokerage_name"] == "", x["brokerage_name"], x["broker_name"]),  # 空经纪公司名排在后面
    )
    # 为泛代理人创建信息并按姓名排序
    referral_brokers = sorted(
        [_create_broker_info(broker) for broker in brokers if not _is_valid_broker(broker)],
        key=lambda x: x["broker_name"],
    )

    leads = sort_data([_flatten_object(lead) for lead in sc.lead_service.get_all_lead_info()], "insurance_type")

    applications = sort_data(
        [_flatten_object(app) for app in sc.insurance_application_service.get_all_application_info()], "brokerage_name"
    )
    brokerage_applications = [app for app in applications if app.get("brokerage_name")]
    non_brokerage_applications = [app for app in applications if not app.get("brokerage_name")]

    sheet_configs = [
        {"name": "代理人列表", "data": qualified_brokers, "fields": _BROKER_FIELDS, "date_columns": []},
        {"name": "泛代理人列表", "data": referral_brokers, "fields": _PAN_AGENT_FIELDS, "date_columns": []},
        {
            "name": "线索信息",
            "data": leads,
            "fields": _LEAD_FIELDS,
            "date_columns": _COMMON_DATE_COLUMNS + ["customer_birthday"],
        },
        {
            "name": "所有订单",
            "data": applications,
            "fields": _APPLICATION_FIELDS,
            "date_columns": _COMMON_DATE_COLUMNS,
        },
        {
            "name": "经纪行订单",
            "data": brokerage_applications,
            "fields": _BROKERAGE_GROUPING_APPLICATION_FIELDS,
            "date_columns": _COMMON_DATE_COLUMNS,
        },
        {
            "name": "非经纪行订单",
            "data": non_brokerage_applications,
            "fields": _NON_BROKERAGE_APPLICATION_FIELDS,
            "date_columns": _COMMON_DATE_COLUMNS,
        },
    ]

    workbook = Workbook()
    workbook.remove(workbook.active)
    for config in sheet_configs:
        sheet = workbook.create_sheet(config["name"])
        _write_sheet_data(
            sheet, config["data"], config["fields"], _FIELD_MAPPINGS, config["date_columns"]  # type:ignore
        )
        _adjust_column_width(sheet)

    output = BytesIO()
    workbook.save(output)
    output.seek(0)

    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
    filename = f"brokerage_report_{timestamp}.xlsx"

    return StreamingResponse(
        output,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"},
    )
