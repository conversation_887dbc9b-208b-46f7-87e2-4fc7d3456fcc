from enum import StrEnum
from typing import Annotated
from typing import Union

from fastapi import APIRouter
from fastapi import Security

from bethune.api.dependencies.authorization import check_not_shared_link
from bethune.api.dto import BaseResponse
from bethune.api.dto.broker import Broker
from bethune.api.dto.broker import BrokerPaymentMethod
from bethune.api.dto.brokerage import BrokerageResponse
from bethune.api.dto.brokerage_user import BrokerageUserResponse
from bethune.api.dto.user import User
from bethune.api.endpoint.insurance.service_context import ServiceContext as InsuranceServiceContext
from bethune.api.endpoint.system.service_context import ServiceContext as SystemServiceContext
from bethune.settings import settings

api_router = APIRouter(tags=["auth"])


class UserInfoTypeEnum(StrEnum):
    PC = "PC"
    MOBILE = "MOBILE"
    SYSTEM = "SYSTEM"


@api_router.get(
    "/user_info",
    summary="Get current user information by type",
    response_model=BaseResponse[Union[Broker, User, BrokerageUserResponse]],
    dependencies=[Security(check_not_shared_link)],
)
async def get_current_user_info(
    sc_system: Annotated[SystemServiceContext, Security(SystemServiceContext.create)],
    sc_insurance: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
) -> BaseResponse[Broker | User | BrokerageUserResponse]:
    user_id = sc_system.current_user.id
    if sc_insurance.brokerage_user_service.get_by_user_id(user_id, True):  # type: ignore
        if not sc_insurance.broker_service.get_by_user_id(user_id, True):  # type: ignore
            return _get_personnel_info(sc_insurance)

    if sc_insurance.broker_service.get_by_user_id(user_id, True):  # type: ignore
        return _get_broker_info(sc_system, sc_insurance)
    return BaseResponse.ok(sc_system.current_user)


def _get_broker_info(sc_system: SystemServiceContext, sc_insurance: InsuranceServiceContext) -> BaseResponse[Broker]:
    broker_id: int = sc_insurance.current_broker.id  # type: ignore
    profile_id = sc_insurance.broker_profile_service.get_by_broker_id(broker_id).id
    is_qualified = sc_insurance.broker_qualification_service.get_by_profile_id(profile_id).is_qualified
    user_id: int = sc_insurance.current_broker.user_id  # type: ignore
    avatar_url = None
    user = sc_system.user_service.get_by_id(user_id)
    if avatar := user.avatar:
        avatar_url = f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{settings.AVATARS_FOLDER}/{avatar}"
    broker_payment_method_model = sc_insurance.broker_payment_method_service.get_by_broker_id(broker_id)
    broker_payment_method = None
    if broker_payment_method_model:
        broker_payment_method = BrokerPaymentMethod.from_model(broker_payment_method_model)

    brokerage = None
    if brokerage_id := sc_insurance.current_broker.brokerage_id:
        if brokerage_model := sc_insurance.brokerage_service.get_by_id(brokerage_id):
            brokerage = BrokerageResponse.from_model(brokerage_model)

    return BaseResponse.ok(
        Broker.from_model(
            sc_insurance.current_broker,
            sc_insurance.permissions,
            is_qualified,
            avatar_url,
            user.email,
            broker_payment_method,
            brokerage,
        )  # type: ignore
    )


def _get_personnel_info(sc_insurance: InsuranceServiceContext) -> BaseResponse[BrokerageUserResponse]:
    return BaseResponse.ok(BrokerageUserResponse.from_model(sc_insurance.current_brokerage_user))


def _build_avatar_url(avatar: str) -> str:
    return f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{settings.AVATARS_FOLDER}/{avatar}"


def _get_broker_payment_method(sc_insurance: InsuranceServiceContext, broker_id: int) -> BrokerPaymentMethod | None:
    payment_method_model = sc_insurance.broker_payment_method_service.get_by_broker_id(broker_id)
    return BrokerPaymentMethod.from_model(payment_method_model) if payment_method_model else None
