from sqlalchemy.exc import NoResultFound
from sqlmodel import delete
from sqlmodel import select

from bethune.db.session_context import session
from bethune.error.errors import NotFoundError
from bethune.model.base import InsuranceType
from bethune.model.broker import Broker<PERSON>eadFee
from bethune.repository.base import BaseRepository


class BrokerLeadFeeRepository(BaseRepository[BrokerLeadFee]):

    def __init__(self):
        super().__init__(model_class=BrokerLeadFee)

    def get_by_broker_id(self, broker_id: int):
        try:
            return session().exec(select(BrokerLeadFee).where(BrokerLeadFee.broker_id == broker_id)).all()
        except NoResultFound:
            raise NotFoundError("BrokerLeadFee not found", detail={"broker_id": broker_id})

    def get_by_broker_id_and_insurance_type(
        self, broker_id: int, insurance_type: InsuranceType
    ) -> BrokerLeadFee | None:
        return (
            session()
            .exec(
                select(BrokerLeadFee)
                .where(BrokerLeadFee.broker_id == broker_id)
                .where(BrokerLeadFee.insurance_type == insurance_type)
            )
            .one_or_none()
        )

    def delete_by_broker_id(self, broker_id: int):
        session().exec(delete(BrokerLeadFee).where(BrokerLeadFee.broker_id == broker_id))
        session().flush()
