from sqlalchemy.exc import NoResultFound
from sqlmodel import select

from bethune.db.session_context import session
from bethune.error.errors import NotFoundError
from bethune.model.broker import BrokerPaymentMethod
from bethune.repository.base import BaseRepository


class BrokerPaymentMethodRepository(BaseRepository[BrokerPaymentMethod]):

    def __init__(self):
        super().__init__(model_class=BrokerPaymentMethod)

    def get_by_broker_id(self, broker_id: int):
        try:
            return session().exec(select(BrokerPaymentMethod).where(BrokerPaymentMethod.broker_id == broker_id)).one()
        except NoResultFound:
            raise NotFoundError("BrokerPaymentMethod not found", detail={"broker_id": broker_id})
