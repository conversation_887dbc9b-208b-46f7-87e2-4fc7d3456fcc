from sqlalchemy.exc import NoResultFound
from sqlmodel import select

from bethune.db.session_context import session
from bethune.error.errors import NotFoundError
from bethune.model.broker import BrokerQualification
from bethune.repository.base import BaseRepository


class BrokerQualificationRepository(BaseRepository[BrokerQualification]):

    def __init__(self):
        super().__init__(model_class=BrokerQualification)

    def get_by_profile_id(self, profile_id: int):
        try:
            return (
                session()
                .exec(select(BrokerQualification).where(BrokerQualification.broker_profile_id == profile_id))
                .one()
            )
        except NoResultFound:
            raise NotFoundError("BrokerQualification not found", detail={"profile_id": profile_id})
