from sqlalchemy.exc import NoResultFound
from sqlmodel import select

from bethune.db.session_context import session
from bethune.error.errors import NotFoundError
from bethune.model.broker import BrokerLeadConfig
from bethune.repository.base import BaseRepository


class BrokerLeadConfigRepository(BaseRepository[BrokerLeadConfig]):

    def __init__(self):
        super().__init__(model_class=BrokerLeadConfig)

    def get_by_broker_id(self, broker_id: int):
        try:
            return session().exec(select(BrokerLeadConfig).where(BrokerLeadConfig.broker_id == broker_id)).one()
        except NoResultFound:
            raise NotFoundError("BrokerLeadConfig not found", detail={"broker_id": broker_id})
