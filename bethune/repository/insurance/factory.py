from .broker import BrokerRepository
from .broker import InsuranceConsultationApplicationRepository
from .broker import InsuranceConsultationCustomerRepository
from .broker import InsuranceConsultationRepository
from .broker import PromotionMaterialRepository
from .broker_lead_config import BrokerLeadConfigRepository
from .broker_lead_fee import BrokerLeadFeeRepository
from .broker_payment_method import BrokerPaymentMethodRepository
from .broker_profile import BrokerProfileRepository
from .broker_qualification import BrokerQualificationRepository
from .brokerage import BrokerageRepository
from .brokerage_trial_application import BrokerageTrialApplicationRepository
from .brokerage_user import BrokerageUserRepository
from .customer import CustomerRepository
from .insurance_application import InsuranceApplicationRepository
from .insurance_company import InsuranceCompanyRepository
from .insurance_policy import InsurancePolicyRepository
from .lead import LeadApplicationRepository
from .lead import LeadReferralFeePaymentRepository
from .lead import LeadRepository
from .user_feedback import UserFeedbackRepository


class InsuranceRepositoryFactory:

    @staticmethod
    def create_insurance_company_repository() -> InsuranceCompanyRepository:
        return InsuranceCompanyRepository()

    @staticmethod
    def create_customer_repository() -> CustomerRepository:
        return CustomerRepository()

    @staticmethod
    def create_broker_repository() -> BrokerRepository:
        return BrokerRepository()

    @staticmethod
    def create_lead_repository() -> LeadRepository:
        return LeadRepository()

    @staticmethod
    def create_lead_referral_fee_payment_repository() -> LeadReferralFeePaymentRepository:
        return LeadReferralFeePaymentRepository()

    @staticmethod
    def create_lead_application_repository() -> LeadApplicationRepository:
        return LeadApplicationRepository()

    @staticmethod
    def create_insurance_application_repository() -> InsuranceApplicationRepository:
        return InsuranceApplicationRepository()

    @staticmethod
    def create_insurance_policy_repository() -> InsurancePolicyRepository:
        return InsurancePolicyRepository()

    @staticmethod
    def create_broker_qualification_repository() -> BrokerQualificationRepository:
        return BrokerQualificationRepository()

    @staticmethod
    def create_broker_profile_repository() -> BrokerProfileRepository:
        return BrokerProfileRepository()

    @staticmethod
    def create_broker_lead_config_repository() -> BrokerLeadConfigRepository:
        return BrokerLeadConfigRepository()

    @staticmethod
    def create_broker_lead_fee_repository() -> BrokerLeadFeeRepository:
        return BrokerLeadFeeRepository()

    @staticmethod
    def create_broker_payment_method_repository() -> BrokerPaymentMethodRepository:
        return BrokerPaymentMethodRepository()

    @staticmethod
    def create_user_feedback_repository() -> UserFeedbackRepository:
        return UserFeedbackRepository()

    @staticmethod
    def create_brokerage_repository() -> BrokerageRepository:
        return BrokerageRepository()

    @staticmethod
    def create_brokerage_user_repository() -> BrokerageUserRepository:
        return BrokerageUserRepository()

    @staticmethod
    def create_brokerage_trial_application_repository() -> BrokerageTrialApplicationRepository:
        return BrokerageTrialApplicationRepository()

    @staticmethod
    def create_insurance_consultation_repository() -> InsuranceConsultationRepository:
        return InsuranceConsultationRepository()

    @staticmethod
    def create_promotion_material_repository() -> PromotionMaterialRepository:
        return PromotionMaterialRepository()

    @staticmethod
    def create_insurance_consultation_application_repository() -> InsuranceConsultationApplicationRepository:
        return InsuranceConsultationApplicationRepository()

    @staticmethod
    def create_insurance_consultation_customer_repository() -> InsuranceConsultationCustomerRepository:
        return InsuranceConsultationCustomerRepository()
